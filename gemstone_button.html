<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Morphing Gemstone Button</title>
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: 
                radial-gradient(circle at 20% 20%, rgba(64, 224, 255, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(138, 43, 226, 0.15) 0%, transparent 50%),
                linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }

        .gemstone-container {
            position: relative;
            perspective: 1200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
        }

        .morphing-gemstone {
            position: relative;
            width: 250px;
            height: 180px;
            cursor: pointer;
            transform-style: preserve-3d;
            transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            filter: 
                drop-shadow(0 15px 35px rgba(0,0,0,0.4))
                drop-shadow(0 0 50px rgba(64, 224, 255, 0.2));
        }

        .morphing-gemstone:hover {
            transform: translateY(-12px) rotateX(8deg) rotateY(5deg) scale(1.1);
            filter: 
                drop-shadow(0 25px 50px rgba(0,0,0,0.5))
                drop-shadow(0 0 80px rgba(64, 224, 255, 0.4))
                drop-shadow(0 0 120px rgba(64, 224, 255, 0.25));
        }

        .gem-shape {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: 
                linear-gradient(45deg, rgba(255,255,255,0.15) 0%, transparent 100%),
                linear-gradient(135deg, 
                    #00f5ff 0%,
                    #40e0ff 15%,
                    #00bfff 30%,
                    #1e90ff 50%,
                    #4169e1 70%,
                    #6a5acd 85%,
                    #8a2be2 100%);
            transition: all 1.2s cubic-bezier(0.23, 1, 0.32, 1);
            overflow: hidden;
            border: 2px solid rgba(255,255,255,0.25);
        }

        /* Shape States */
        .shape-octagon {
            width: 140px;
            height: 140px;
            clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);
        }

        .shape-emerald {
            width: 160px;
            height: 120px;
            clip-path: polygon(12% 0%, 88% 0%, 100% 12%, 100% 88%, 88% 100%, 12% 100%, 0% 88%, 0% 12%);
        }

        .shape-rectangle {
            width: 240px;
            height: 100px;
            clip-path: polygon(8% 0%, 92% 0%, 100% 8%, 100% 92%, 92% 100%, 8% 100%, 0% 92%, 0% 8%);
        }

        .shape-triangle {
            width: 120px;
            height: 120px;
            clip-path: polygon(50% 0%, 95% 70%, 75% 100%, 25% 100%, 5% 70%);
        }

        .shape-diamond {
            width: 130px;
            height: 130px;
            clip-path: polygon(50% 0%, 80% 15%, 100% 50%, 80% 85%, 50% 100%, 20% 85%, 0% 50%, 20% 15%);
        }

        .shape-heart {
            width: 150px;
            height: 140px;
            clip-path: polygon(50% 25%, 25% 0%, 0% 25%, 0% 62.5%, 50% 100%, 100% 62.5%, 100% 25%, 75% 0%);
        }

        /* Advanced Crystal Effects */
        .crystal-layer {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            clip-path: inherit;
            pointer-events: none;
        }

        .crystal-facets {
            background: 
                linear-gradient(45deg, 
                    rgba(255,255,255,0.9) 0%,
                    rgba(255,255,255,0.4) 15%,
                    transparent 25%,
                    transparent 75%,
                    rgba(255,255,255,0.3) 85%,
                    rgba(255,255,255,0.8) 100%);
        }

        .crystal-highlight {
            background: 
                radial-gradient(ellipse at 30% 20%, 
                    rgba(255,255,255,0.95) 0%,
                    rgba(255,255,255,0.7) 20%,
                    rgba(255,255,255,0.3) 50%,
                    transparent 100%);
            filter: blur(8px);
            opacity: 0.9;
        }

        .light-refraction {
            background: 
                linear-gradient(60deg, 
                    transparent 0%,
                    rgba(255,0,150,0.15) 20%,
                    rgba(0,255,255,0.15) 40%,
                    rgba(255,255,0,0.15) 60%,
                    rgba(150,0,255,0.15) 80%,
                    transparent 100%);
            opacity: 0;
            transition: all 0.8s ease;
            animation: refract 3s infinite;
        }

        @keyframes refract {
            0%, 100% { transform: translateX(-150%) rotate(0deg); opacity: 0; }
            30% { opacity: 0.8; }
            70% { opacity: 0.8; }
            100% { transform: translateX(150%) rotate(5deg); opacity: 0; }
        }

        /* Dynamic Sparkle System */
        .sparkle-system {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
        }

        .sparkle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: radial-gradient(circle, #ffffff 0%, #40e0ff 70%, transparent 100%);
            border-radius: 50%;
            opacity: 0;
            animation: dynamicSparkle 4s infinite;
            box-shadow: 0 0 10px rgba(255,255,255,0.8);
        }

        @keyframes dynamicSparkle {
            0%, 100% { 
                opacity: 0; 
                transform: scale(0) rotate(0deg); 
            }
            20% { 
                opacity: 1; 
                transform: scale(1.5) rotate(90deg); 
                box-shadow: 0 0 20px rgba(255,255,255,1);
            }
            40% { 
                opacity: 0.7; 
                transform: scale(1) rotate(180deg); 
            }
            60% { 
                opacity: 1; 
                transform: scale(1.8) rotate(270deg); 
                box-shadow: 0 0 25px rgba(64, 224, 255, 0.9);
            }
            80% { 
                opacity: 0.5; 
                transform: scale(0.8) rotate(360deg); 
            }
        }

        /* Multiple sparkles positioned dynamically */
        .sparkle:nth-child(1) { top: 25%; left: 30%; animation-delay: 0s; }
        .sparkle:nth-child(2) { top: 60%; right: 25%; animation-delay: 1.3s; }
        .sparkle:nth-child(3) { top: 40%; left: 60%; animation-delay: 2.1s; }
        .sparkle:nth-child(4) { bottom: 30%; left: 25%; animation-delay: 3.2s; }
        .sparkle:nth-child(5) { top: 70%; right: 40%; animation-delay: 0.7s; }
        .sparkle:nth-child(6) { top: 35%; right: 60%; animation-delay: 1.8s; }

        /* Morphing Text */
        .gem-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: 700;
            font-size: 16px;
            text-shadow: 
                0 3px 12px rgba(0,0,0,1),
                0 0 15px rgba(64, 224, 255, 0.4);
            pointer-events: none;
            z-index: 15;
            letter-spacing: 2px;
            text-transform: uppercase;
            transition: all 0.6s ease;
            opacity: 0.95;
        }

        .morphing-gemstone:hover .gem-text {
            transform: translate(-50%, -50%) scale(1.1);
            text-shadow: 
                0 3px 15px rgba(0,0,0,1),
                0 0 20px rgba(64, 224, 255, 0.7),
                0 0 30px rgba(255,255,255, 0.4);
        }

        /* Control Panel */
        .control-panel {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
        }

        .shape-btn {
            background: linear-gradient(135deg, 
                rgba(64, 224, 255, 0.2) 0%,
                rgba(30, 144, 255, 0.3) 50%,
                rgba(138, 43, 226, 0.2) 100%);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .shape-btn:hover {
            background: linear-gradient(135deg, 
                rgba(64, 224, 255, 0.4) 0%,
                rgba(30, 144, 255, 0.5) 50%,
                rgba(138, 43, 226, 0.4) 100%);
            border-color: rgba(255,255,255,0.6);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(64, 224, 255, 0.3);
        }

        .shape-btn.active {
            background: linear-gradient(135deg, 
                rgba(64, 224, 255, 0.6) 0%,
                rgba(30, 144, 255, 0.7) 50%,
                rgba(138, 43, 226, 0.6) 100%);
            border-color: rgba(255,255,255,0.8);
            box-shadow: 0 0 25px rgba(64, 224, 255, 0.5);
        }

        /* Auto-morph indicator */
        .auto-morph-toggle {
            background: linear-gradient(135deg, 
                rgba(255, 165, 0, 0.3) 0%,
                rgba(255, 69, 0, 0.4) 100%);
            border: 2px solid rgba(255, 165, 0, 0.5);
        }

        .auto-morph-toggle:hover {
            background: linear-gradient(135deg, 
                rgba(255, 165, 0, 0.5) 0%,
                rgba(255, 69, 0, 0.6) 100%);
            box-shadow: 0 6px 20px rgba(255, 165, 0, 0.4);
        }

        .auto-morph-toggle.active {
            background: linear-gradient(135deg, 
                rgba(255, 165, 0, 0.7) 0%,
                rgba(255, 69, 0, 0.8) 100%);
            border-color: rgba(255, 165, 0, 0.9);
            box-shadow: 0 0 25px rgba(255, 165, 0, 0.6);
        }

        /* Floating ambient particles */
        .ambient-particle {
            position: fixed;
            width: 3px;
            height: 3px;
            background: rgba(64, 224, 255, 0.6);
            border-radius: 50%;
            pointer-events: none;
            animation: float 12s infinite linear;
            z-index: -1;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% {
                transform: translateY(-10vh) rotate(360deg);
                opacity: 0;
            }
        }

        /* Enhanced hover glow */
        .morphing-gemstone:hover .gem-shape {
            background: 
                linear-gradient(45deg, rgba(255,255,255,0.25) 0%, transparent 100%),
                linear-gradient(135deg, 
                    #00f5ff 0%,
                    #40e0ff 12%,
                    #00bfff 25%,
                    #1e90ff 45%,
                    #4169e1 65%,
                    #6a5acd 80%,
                    #9370db 95%,
                    #8a2be2 100%);
            box-shadow: 
                inset 0 0 40px rgba(255,255,255,0.3),
                inset 0 0 60px rgba(64, 224, 255, 0.2),
                0 0 50px rgba(64, 224, 255, 0.4);
        }
    </style>
</head>
<body>
    <!-- Ambient particles -->
    <div class="ambient-particle" style="left: 5%; animation-delay: 0s;"></div>
    <div class="ambient-particle" style="left: 25%; animation-delay: 3s;"></div>
    <div class="ambient-particle" style="left: 45%; animation-delay: 6s;"></div>
    <div class="ambient-particle" style="left: 65%; animation-delay: 9s;"></div>
    <div class="ambient-particle" style="left: 85%; animation-delay: 2s;"></div>
    <div class="ambient-particle" style="left: 95%; animation-delay: 5s;"></div>

    <div class="gemstone-container">
        <!-- Morphing Gemstone Button -->
        <div class="morphing-gemstone" onclick="handleGemClick()">
            <div class="gem-shape shape-octagon" id="morphingGem">
                <div class="crystal-layer crystal-facets"></div>
                <div class="crystal-layer crystal-highlight"></div>
                <div class="crystal-layer light-refraction"></div>
                
                <div class="sparkle-system">
                    <div class="sparkle"></div>
                    <div class="sparkle"></div>
                    <div class="sparkle"></div>
                    <div class="sparkle"></div>
                    <div class="sparkle"></div>
                    <div class="sparkle"></div>
                </div>
            </div>
            <span class="gem-text" id="gemText">Diamond</span>
        </div>

        <!-- Control Panel -->
        <div class="control-panel">
            <button class="shape-btn active" onclick="morphToShape('octagon', 'Diamond', this)">Diamond</button>
            <button class="shape-btn" onclick="morphToShape('emerald', 'Emerald', this)">Emerald</button>
            <button class="shape-btn" onclick="morphToShape('rectangle', 'Sapphire', this)">Sapphire</button>
            <button class="shape-btn" onclick="morphToShape('triangle', 'Topaz', this)">Topaz</button>
            <button class="shape-btn" onclick="morphToShape('diamond', 'Ruby', this)">Ruby</button>
            <button class="shape-btn" onclick="morphToShape('heart', 'Amethyst', this)">Amethyst</button>
            <button class="shape-btn auto-morph-toggle" onclick="toggleAutoMorph(this)" id="autoBtn">Auto Morph</button>
        </div>
    </div>

    <script>
        let currentShapeIndex = 0;
        let autoMorphInterval = null;
        let isAutoMorphing = false;
        
        const shapes = [
            { class: 'shape-octagon', name: 'Diamond' },
            { class: 'shape-emerald', name: 'Emerald' },
            { class: 'shape-rectangle', name: 'Sapphire' },
            { class: 'shape-triangle', name: 'Topaz' },
            { class: 'shape-diamond', name: 'Ruby' },
            { class: 'shape-heart', name: 'Amethyst' }
        ];

        function morphToShape(shapeClass, gemName, button) {
            const gem = document.getElementById('morphingGem');
            const text = document.getElementById('gemText');
            
            // Remove all shape classes
            shapes.forEach(shape => {
                gem.classList.remove(shape.class);
            });
            
            // Add new shape class
            gem.classList.add(`shape-${shapeClass}`);
            
            // Update text with fade effect
            text.style.opacity = '0';
            text.style.transform = 'translate(-50%, -50%) scale(0.8)';
            
            setTimeout(() => {
                text.textContent = gemName;
                text.style.opacity = '0.95';
                text.style.transform = 'translate(-50%, -50%) scale(1)';
            }, 300);
            
            // Update active button
            document.querySelectorAll('.shape-btn:not(.auto-morph-toggle)').forEach(btn => {
                btn.classList.remove('active');
            });
            if (button && !button.classList.contains('auto-morph-toggle')) {
                button.classList.add('active');
            }
            
            // Update current shape index
            currentShapeIndex = shapes.findIndex(shape => shape.class === `shape-${shapeClass}`);
            
            // Create morph effect
            createMorphEffect();
        }

        function toggleAutoMorph(button) {
            if (isAutoMorphing) {
                clearInterval(autoMorphInterval);
                isAutoMorphing = false;
                button.classList.remove('active');
                button.textContent = 'Auto Morph';
            } else {
                isAutoMorphing = true;
                button.classList.add('active');
                button.textContent = 'Stop Auto';
                
                autoMorphInterval = setInterval(() => {
                    currentShapeIndex = (currentShapeIndex + 1) % shapes.length;
                    const nextShape = shapes[currentShapeIndex];
                    const shapeClass = nextShape.class.replace('shape-', '');
                    morphToShape(shapeClass, nextShape.name, null);
                }, 2500);
            }
        }

        function createMorphEffect() {
            const gem = document.getElementById('morphingGem');
            
            // Morph wave effect
            const wave = document.createElement('div');
            wave.style.position = 'absolute';
            wave.style.top = '0';
            wave.style.left = '0';
            wave.style.right = '0';
            wave.style.bottom = '0';
            wave.style.background = `
                radial-gradient(circle at center, 
                    rgba(255,255,255,0.8) 0%,
                    rgba(64,224,255,0.6) 30%,
                    rgba(138,43,226,0.4) 60%,
                    transparent 100%)`;
            wave.style.clipPath = getComputedStyle(gem).clipPath;
            wave.style.animation = 'morphWave 0.8s ease-out';
            wave.style.pointerEvents = 'none';
            wave.style.zIndex = '20';
            
            gem.appendChild(wave);
            
            // Sparkle burst during morph
            for (let i = 0; i < 20; i++) {
                const sparkle = document.createElement('div');
                sparkle.style.position = 'absolute';
                sparkle.style.width = Math.random() * 8 + 4 + 'px';
                sparkle.style.height = sparkle.style.width;
                
                const colors = ['#ffffff', '#40e0ff', '#00bfff', '#8a2be2', '#ff69b4', '#00ff7f'];
                sparkle.style.background = colors[Math.floor(Math.random() * colors.length)];
                sparkle.style.borderRadius = '50%';
                sparkle.style.pointerEvents = 'none';
                sparkle.style.left = '50%';
                sparkle.style.top = '50%';
                sparkle.style.boxShadow = `0 0 15px ${sparkle.style.background}`;
                sparkle.style.zIndex = '25';
                
                const angle = (i * (360 / 20)) * Math.PI / 180;
                const distance = Math.random() * 60 + 40;
                const x = Math.cos(angle) * distance;
                const y = Math.sin(angle) * distance;
                
                sparkle.style.animation = `morphSparkle 1.2s ease-out forwards`;
                sparkle.style.setProperty('--x', x + 'px');
                sparkle.style.setProperty('--y', y + 'px');
                sparkle.style.setProperty('--rotation', Math.random() * 720 + 'deg');
                
                gem.appendChild(sparkle);
                
                setTimeout(() => sparkle.remove(), 1200);
            }
            
            setTimeout(() => wave.remove(), 800);
        }

        function handleGemClick() {
            if (!isAutoMorphing) {
                currentShapeIndex = (currentShapeIndex + 1) % shapes.length;
                const nextShape = shapes[currentShapeIndex];
                const shapeClass = nextShape.class.replace('shape-', '');
                morphToShape(shapeClass, nextShape.name, null);
            }
            
            // Enhanced click effect
            const gem = document.getElementById('morphingGem');
            
            // Intense flash
            const flash = document.createElement('div');
            flash.style.position = 'absolute';
            flash.style.top = '0';
            flash.style.left = '0';
            flash.style.right = '0';
            flash.style.bottom = '0';
            flash.style.background = 'rgba(255,255,255,0.9)';
            flash.style.clipPath = getComputedStyle(gem).clipPath;
            flash.style.animation = 'intenseFlash 0.4s ease-out';
            flash.style.pointerEvents = 'none';
            flash.style.zIndex = '30';
            
            gem.appendChild(flash);
            
            setTimeout(() => flash.remove(), 400);
        }

        // Enhanced CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes morphWave {
                0% { 
                    opacity: 0; 
                    transform: scale(0.5) rotate(0deg);
                }
                50% { 
                    opacity: 1; 
                    transform: scale(1.2) rotate(180deg);
                }
                100% { 
                    opacity: 0; 
                    transform: scale(1.5) rotate(360deg);
                }
            }
            
            @keyframes morphSparkle {
                0% {
                    transform: translate(-50%, -50%) scale(0) rotate(0deg);
                    opacity: 1;
                }
                60% {
                    opacity: 1;
                    transform: translate(calc(-50% + var(--x) * 0.8), calc(-50% + var(--y) * 0.8)) scale(1.5) rotate(calc(var(--rotation) * 0.6));
                }
                100% {
                    transform: translate(calc(-50% + var(--x)), calc(-50% + var(--y))) scale(0) rotate(var(--rotation));
                    opacity: 0;
                }
            }
            
            @keyframes intenseFlash {
                0% { 
                    opacity: 0; 
                    transform: scale(0.9);
                }
                30% { 
                    opacity: 1; 
                    transform: scale(1.1);
                }
                100% { 
                    opacity: 0; 
                    transform: scale(1.3);
                }
            }
        `;
        document.head.appendChild(style);

        // Enhanced 3D mouse tracking
        document.addEventListener('mousemove', (e) => {
            const gem = document.querySelector('.morphing-gemstone');
            const rect = gem.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            
            const mouseX = e.clientX - centerX;
            const mouseY = e.clientY - centerY;
            
            const distance = Math.sqrt(mouseX * mouseX + mouseY * mouseY);
            const maxDistance = 300;
            
            if (distance < maxDistance) {
                const intensity = (maxDistance - distance) / maxDistance;
                const rotateX = (mouseY / maxDistance) * 20 * intensity;
                const rotateY = -(mouseX / maxDistance) * 20 * intensity;
                const translateZ = intensity * 15;
                
                gem.style.transform = `
                    translateY(-12px) 
                    translateZ(${translateZ}px) 
                    rotateX(${rotateX}deg) 
                    rotateY(${rotateY}deg) 
                    scale(${1.1 + intensity * 0.08})
                `;
            }
        });

        // Initialize auto-morph after 5 seconds
        setTimeout(() => {
            const autoBtn = document.getElementById('autoBtn');
            toggleAutoMorph(autoBtn);
        }, 5000);
    </script>
</body>
</html>