// Living Gem Hero <PERSON>tons Interactive Effects
// This script adds mouse tracking, 3D transforms, unstable core events, and particle effects

function initializeLivingGemButtons() {
    // Generate star shadows for twinkling effect
    function generateStarShadows(starCount, width, height) {
        let shadows = '';
        for (let i = 0; i < starCount; i++) {
            shadows += `${Math.random() * width}px ${Math.random() * height}px #fff,`;
        }
        return shadows.slice(0, -1);
    }
    
    // Set up star shadows CSS variables
    const buttonWidth = 160; // 10rem in pixels (approximately)
    const buttonHeight = 44;  // 2.75rem in pixels (approximately)
    document.documentElement.style.setProperty('--star-shadows', generateStarShadows(30, buttonWidth, buttonHeight));
    document.documentElement.style.setProperty('--star-shadows-2', generateStarShadows(30, buttonWidth, buttonHeight));

    // Initialize each button wrapper
    document.querySelectorAll('.hero-button-wrapper').forEach(wrapper => {
        const button = wrapper.querySelector('.hero-button, .hero-button-secondary');
        if (!button) return;
        
        let isHovering = false;
        let isUnstable = false;
        
        // Mouse tracking for 3D tilt effect
        wrapper.addEventListener('mousemove', (e) => {
            if (isUnstable) return;
            
            const rect = wrapper.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            // Calculate rotation based on mouse position
            const rotateX = (y - centerY) / 5;
            const rotateY = (x - centerX) / -5;
            
            button.style.transform = `rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
            
            // Set mouse position for radial gradient
            button.style.setProperty('--mouse-x', `${(x / rect.width) * 100}%`);
            button.style.setProperty('--mouse-y', `${(y / rect.height) * 100}%`);
        });

        wrapper.addEventListener('mouseenter', () => {
            isHovering = true;
        });

        wrapper.addEventListener('mouseleave', () => {
            isHovering = false;
            button.style.transform = `rotateX(0deg) rotateY(0deg)`;
        });

        // Click effects
        button.addEventListener('click', (e) => {
            createShockwave(e.clientX, e.clientY);
            createParticles(e.clientX, e.clientY);
        });

        // Unstable core events
        function triggerUnstableEvent() {
            if (isHovering || isUnstable) return;

            isUnstable = true;
            
            button.classList.add('is-unstable');
            createVentingShards(button);

            setTimeout(() => {
                button.classList.remove('is-unstable');
                isUnstable = false;
            }, 400);
        }
        
        // Random unstable events
        setInterval(triggerUnstableEvent, 2000 + Math.random() * 1000);
    });

    // Create venting shards effect
    function createVentingShards(button) {
        const rect = button.getBoundingClientRect();
        for (let i = 0; i < 5; i++) {
            const shard = document.createElement('div');
            shard.classList.add('venting-shard');
            document.body.appendChild(shard);

            const size = Math.floor(Math.random() * 6 + 3);
            shard.style.width = `${size}px`;
            shard.style.height = `${size}px`;

            const angle = Math.random() * 360;
            const startX = rect.left + rect.width / 2;
            const startY = rect.top + rect.height / 2;
            
            shard.style.left = `${startX}px`;
            shard.style.top = `${startY}px`;
            
            const travelDist = 50;
            const endX = travelDist * Math.cos(angle * Math.PI / 180);
            const endY = travelDist * Math.sin(angle * Math.PI / 180);

            setTimeout(() => {
                shard.style.transform = `translate(${endX}px, ${endY}px)`;
                shard.style.opacity = '0';
            }, 10);

            setTimeout(() => shard.remove(), 500);
        }
    }

    // Create shockwave effect on click
    function createShockwave(x, y) {
        const shockwave = document.createElement('div');
        shockwave.classList.add('shockwave');
        document.body.appendChild(shockwave);
        
        shockwave.style.left = `${x}px`;
        shockwave.style.top = `${y}px`;
        shockwave.style.width = '20px';
        shockwave.style.height = '20px';
        shockwave.style.background = `radial-gradient(circle, transparent, #ffd700 80%)`;
        shockwave.style.transform = 'translate(-50%, -50%) scale(0)';
        shockwave.style.opacity = '1';
        
        setTimeout(() => {
            shockwave.style.transform = 'translate(-50%, -50%) scale(2)';
            shockwave.style.opacity = '0';
        }, 10);
        
        setTimeout(() => shockwave.remove(), 300);
    }

    // Create particle explosion on click
    function createParticles(x, y) {
        const particleCount = 25;
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.classList.add('particle');
            document.body.appendChild(particle);
            
            const size = Math.floor(Math.random() * 8 + 4);
            const hue = Math.random() > 0.5 ? 45 + Math.random() * 10 : 200 + Math.random() * 20;
            
            particle.style.width = `${size}px`;
            particle.style.height = `${size}px`;
            particle.style.background = `hsl(${hue}, 100%, 75%)`;
            particle.style.left = `${x}px`;
            particle.style.top = `${y}px`;
            
            const angle = Math.random() * 360;
            const distance = Math.random() * 100 + 70;
            const translateX = distance * Math.cos(angle * Math.PI / 180);
            const translateY = distance * Math.sin(angle * Math.PI / 180);
            
            setTimeout(() => {
                particle.style.transform = `translate(-50%, -50%) translate(${translateX}px, ${translateY}px) scale(0)`;
                particle.style.opacity = '0';
            }, 10);
            
            setTimeout(() => particle.remove(), 1000);
        }
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeLivingGemButtons);
} else {
    initializeLivingGemButtons();
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { initializeLivingGemButtons };
}
