'use client';

import React, { useRef, useEffect, useState } from 'react';
import * as THREE from 'three';

interface ThreeJSButtonProps {
  buttonId: string;
  isPrimary?: boolean;
  className?: string;
}

const ThreeJSButton: React.FC<ThreeJSButtonProps> = ({ 
  buttonId, 
  isPrimary = true, 
  className = '' 
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const cubeRef = useRef<THREE.Mesh | null>(null);
  const animationIdRef = useRef<number | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (!canvasRef.current) return;

    // Initialize Three.js scene
    const scene = new THREE.Scene();
    sceneRef.current = scene;

    // Create camera with perspective matching CSS perspective
    const camera = new THREE.PerspectiveCamera(
      50, // FOV to match CSS perspective: 250px
      1, // Aspect ratio (will be updated)
      0.1,
      1000
    );
    camera.position.set(0, 0, 5);

    // Create renderer with responsive sizing
    const renderer = new THREE.WebGLRenderer({
      canvas: canvasRef.current,
      alpha: true,
      antialias: true,
    });

    // Responsive button sizing
    const updateSize = () => {
      const buttonElement = document.querySelector(`[data-button-id="${buttonId}"]`);
      if (buttonElement) {
        const rect = buttonElement.getBoundingClientRect();
        renderer.setSize(rect.width, rect.height);
        camera.aspect = rect.width / rect.height;
        camera.updateProjectionMatrix();
      } else {
        // Fallback sizes
        renderer.setSize(160, 44);
      }
    };

    updateSize();
    renderer.setClearColor(0x000000, 0); // Transparent background
    rendererRef.current = renderer;

    // Handle window resize
    const handleResize = () => updateSize();
    window.addEventListener('resize', handleResize);

    // Create 3D cube geometry with rounded edges
    const geometry = new THREE.BoxGeometry(2.8, 0.8, 0.6, 8, 8, 8);

    // Create realistic materials for different faces with gradients
    const materials = [
      // Right face - Darker for depth
      new THREE.MeshPhongMaterial({
        color: isPrimary ? 0xd4ac0d : 0x2980b9,
        transparent: true,
        opacity: 0.8,
        shininess: 100,
        specular: 0x444444,
      }),
      // Left face - Darker for depth
      new THREE.MeshPhongMaterial({
        color: isPrimary ? 0xb7950b : 0x1f618d,
        transparent: true,
        opacity: 0.8,
        shininess: 100,
        specular: 0x444444,
      }),
      // Top face - Brightest (light source from above)
      new THREE.MeshPhongMaterial({
        color: isPrimary ? 0xf8e71c : 0x5dade2,
        transparent: true,
        opacity: 0.9,
        shininess: 150,
        specular: 0x666666,
      }),
      // Bottom face - Darkest (shadow)
      new THREE.MeshPhongMaterial({
        color: isPrimary ? 0x9a7c0a : 0x154360,
        transparent: true,
        opacity: 0.7,
        shininess: 50,
        specular: 0x222222,
      }),
      // Front face - Main visible face
      new THREE.MeshPhongMaterial({
        color: isPrimary ? 0xf4d03f : 0x4a90e2,
        transparent: true,
        opacity: 0.9,
        shininess: 120,
        specular: 0x555555,
      }),
      // Back face - Hidden, darker
      new THREE.MeshPhongMaterial({
        color: isPrimary ? 0xb7950b : 0x1f618d,
        transparent: true,
        opacity: 0.6,
        shininess: 80,
        specular: 0x333333,
      }),
    ];

    const cube = new THREE.Mesh(geometry, materials);
    cube.position.set(0, 0, 0);
    cubeRef.current = cube;
    scene.add(cube);

    // Add professional lighting setup
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
    scene.add(ambientLight);

    // Main directional light (from top-left)
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
    directionalLight.position.set(3, 3, 5);
    directionalLight.castShadow = true;
    scene.add(directionalLight);

    // Secondary light for fill (from bottom-right)
    const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
    fillLight.position.set(-2, -1, 3);
    scene.add(fillLight);

    // Rim light for edge definition
    const rimLight = new THREE.DirectionalLight(isPrimary ? 0xffd700 : 0x4a90e2, 0.5);
    rimLight.position.set(-3, 0, -2);
    scene.add(rimLight);

    // Animation loop with enhanced effects and performance optimization
    let lastTime = 0;
    const targetFPS = 60;
    const frameInterval = 1000 / targetFPS;

    const animate = (currentTime: number) => {
      animationIdRef.current = requestAnimationFrame(animate);

      // Throttle animation to target FPS for performance
      if (currentTime - lastTime < frameInterval) return;
      lastTime = currentTime;

      if (cubeRef.current) {
        // Subtle floating animation
        const time = currentTime * 0.001;
        cubeRef.current.position.y = Math.sin(time * 0.5) * 0.05;
        cubeRef.current.rotation.y = Math.sin(time * 0.3) * 0.1;
        cubeRef.current.rotation.x = Math.cos(time * 0.2) * 0.05;

        // Subtle scale breathing effect
        const scale = 1 + Math.sin(time * 0.8) * 0.02;
        cubeRef.current.scale.set(scale, scale, scale);
      }

      renderer.render(scene, camera);
    };

    animate(0);
    setIsInitialized(true);

    // Cleanup function
    return () => {
      window.removeEventListener('resize', handleResize);
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
      }
      if (rendererRef.current) {
        rendererRef.current.dispose();
      }
      if (sceneRef.current) {
        sceneRef.current.clear();
      }
    };
  }, [isPrimary]);

  // Handle button interactions with enhanced 3D effects
  useEffect(() => {
    if (!isInitialized) return;

    const buttonElement = document.querySelector(`[data-button-id="${buttonId}"]`);
    if (!buttonElement) return;

    let isHovering = false;
    let mouseX = 0;
    let mouseY = 0;

    const handleMouseEnter = () => {
      isHovering = true;
      if (cubeRef.current) {
        // Enhanced 3D lift and rotation on hover
        cubeRef.current.position.z = 0.3;
        cubeRef.current.scale.set(1.05, 1.05, 1.05);
      }
    };

    const handleMouseLeave = () => {
      isHovering = false;
      if (cubeRef.current) {
        // Return to normal position
        cubeRef.current.position.z = 0;
        cubeRef.current.scale.set(1, 1, 1);
        cubeRef.current.rotation.x = 0;
        cubeRef.current.rotation.y = 0;
      }
    };

    const handleMouseMove = (event: MouseEvent) => {
      if (!isHovering || !cubeRef.current) return;

      const rect = buttonElement.getBoundingClientRect();
      mouseX = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      mouseY = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      // Apply 3D rotation based on mouse position
      cubeRef.current.rotation.y = mouseX * 0.3;
      cubeRef.current.rotation.x = mouseY * 0.2;
    };

    const handleClick = () => {
      if (cubeRef.current) {
        // Click animation - quick scale down and up
        cubeRef.current.scale.set(0.95, 0.95, 0.95);
        setTimeout(() => {
          if (cubeRef.current) {
            cubeRef.current.scale.set(1.05, 1.05, 1.05);
          }
        }, 100);
      }
    };

    buttonElement.addEventListener('mouseenter', handleMouseEnter);
    buttonElement.addEventListener('mouseleave', handleMouseLeave);
    buttonElement.addEventListener('mousemove', handleMouseMove);
    buttonElement.addEventListener('click', handleClick);

    return () => {
      buttonElement.removeEventListener('mouseenter', handleMouseEnter);
      buttonElement.removeEventListener('mouseleave', handleMouseLeave);
      buttonElement.removeEventListener('mousemove', handleMouseMove);
      buttonElement.removeEventListener('click', handleClick);
    };
  }, [isInitialized, buttonId]);

  // Handle unstable/crack events integration
  useEffect(() => {
    if (!isInitialized) return;

    const buttonElement = document.querySelector(`[data-button-id="${buttonId}"]`);
    if (!buttonElement) return;

    // Observer to watch for 'is-unstable' class changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          const target = mutation.target as Element;
          if (target.classList.contains('is-unstable') && cubeRef.current) {
            // 3D crack effect - dramatic rotation and scale
            const originalRotationX = cubeRef.current.rotation.x;
            const originalRotationY = cubeRef.current.rotation.y;
            const originalScale = cubeRef.current.scale.x;

            // Intense shake effect in 3D
            cubeRef.current.rotation.x = originalRotationX + (Math.random() - 0.5) * 0.3;
            cubeRef.current.rotation.y = originalRotationY + (Math.random() - 0.5) * 0.3;
            cubeRef.current.rotation.z = (Math.random() - 0.5) * 0.2;
            cubeRef.current.scale.set(0.9, 0.9, 0.9);

            // Return to normal after crack effect
            setTimeout(() => {
              if (cubeRef.current) {
                cubeRef.current.rotation.x = originalRotationX;
                cubeRef.current.rotation.y = originalRotationY;
                cubeRef.current.rotation.z = 0;
                cubeRef.current.scale.set(originalScale, originalScale, originalScale);
              }
            }, 400);
          }
        }
      });
    });

    observer.observe(buttonElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => {
      observer.disconnect();
    };
  }, [isInitialized, buttonId]);

  return (
    <canvas
      ref={canvasRef}
      className={`absolute inset-0 pointer-events-none ${className}`}
      style={{
        zIndex: -1,
        width: '100%',
        height: '100%',
      }}
    />
  );
};

export default ThreeJSButton;
