/* Related Products Carousel Styles */
@keyframes scroll-carousel {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.related-products-carousel {
  animation: scroll-carousel 20s linear infinite;
  will-change: transform;
}

.related-products-carousel:hover {
  animation-play-state: paused;
}

/* Manual control mode - no auto animation */
.related-products-manual {
  will-change: transform;
  /* No animation when in manual mode */
}

/* Mobile Breakpoint (default) */
#related-products-carousel {
  max-width: 85%; /* Limit max-width for mobile */
  margin: 0 auto;
}

#related-products-main-container .flex {
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

#related-products-main-container .flex-shrink-0 {
  width: calc(50% - 1rem);
}

/* Responsive adjustments */
@media (min-width: 768px) {
  .related-products-carousel {
    animation-duration: 25s;
  }

  #related-products-carousel {
    max-width: 90%; /* Limit max-width for tablet */
    margin: 0 auto;
  }

  #related-products-main-container .flex {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.25rem;
  }

  #related-products-main-container .flex-shrink-0 {
    width: calc(33.333% - 1.25rem);
  }
}

/* Laptop Breakpoint (1024px) */
@media (min-width: 1024px) {
  #related-products-carousel {
    max-width: 80%; /* Further limit max-width for laptop */
    margin: 0 auto;
  }

  #related-products-main-container .flex {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }

  #related-products-main-container .flex-shrink-0 {
    width: calc(25% - 1.5rem);
  }
}

/* Smooth scrolling performance */
.related-products-carousel,
.related-products-manual {
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
} 