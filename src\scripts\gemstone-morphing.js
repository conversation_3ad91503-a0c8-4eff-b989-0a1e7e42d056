// Gemstone Shape Morphing System for Hero Buttons
// Automatically cycles through different gemstone shapes with crystal effects

function initializeGemstoneorphing() {
    const shapes = [
        { class: 'shape-octagon', name: 'Diamond' },
        { class: 'shape-emerald', name: 'Emerald' },
        { class: 'shape-rectangle', name: 'Sapphire' },
        { class: 'shape-diamond', name: '<PERSON>' },
        { class: 'shape-heart', name: 'Amethyst' }
    ];

    let currentShapeIndex1 = 0; // First button starts with octagon
    let currentShapeIndex2 = 1; // Second button starts with emerald
    
    // Get all morphing button elements
    const morphingButtons = [
        document.getElementById('morphingGem1'),
        document.getElementById('morphingGem2'),
        document.getElementById('morphingGemMain1'),
        document.getElementById('morphingGemMain2')
    ].filter(button => button !== null);

    function morphToShape(button, shapeClass, isFirstButton = true) {
        if (!button) return;

        // Remove all shape classes
        shapes.forEach(shape => {
            button.classList.remove(shape.class);
        });

        // Add new shape class
        button.classList.add(shapeClass);

        // Create morph effect
        createMorphEffect(button);
    }

    function createMorphEffect(button) {
        if (!button) return;

        // Morph wave effect
        const wave = document.createElement('div');
        wave.style.position = 'absolute';
        wave.style.top = '0';
        wave.style.left = '0';
        wave.style.right = '0';
        wave.style.bottom = '0';
        wave.style.background = `
            radial-gradient(circle at center,
                rgba(255,255,255,0.8) 0%,
                rgba(64,224,255,0.6) 30%,
                rgba(138,43,226,0.4) 60%,
                transparent 100%)`;
        wave.style.clipPath = getComputedStyle(button).clipPath;
        wave.style.animation = 'morphWave 0.8s ease-out';
        wave.style.pointerEvents = 'none';
        wave.style.zIndex = '20';

        button.appendChild(wave);
        
        // Sparkle burst during morph
        for (let i = 0; i < 15; i++) {
            const sparkle = document.createElement('div');
            sparkle.style.position = 'absolute';
            sparkle.style.width = Math.random() * 6 + 3 + 'px';
            sparkle.style.height = sparkle.style.width;
            
            const colors = ['#ffffff', '#40e0ff', '#00bfff', '#8a2be2', '#ff69b4', '#00ff7f'];
            sparkle.style.background = colors[Math.floor(Math.random() * colors.length)];
            sparkle.style.borderRadius = '50%';
            sparkle.style.pointerEvents = 'none';
            sparkle.style.left = '50%';
            sparkle.style.top = '50%';
            sparkle.style.boxShadow = `0 0 15px ${sparkle.style.background}`;
            sparkle.style.zIndex = '25';
            
            const angle = (i * (360 / 15)) * Math.PI / 180;
            const distance = Math.random() * 40 + 30;
            const x = Math.cos(angle) * distance;
            const y = Math.sin(angle) * distance;
            
            sparkle.style.animation = `morphSparkle 1.2s ease-out forwards`;
            sparkle.style.setProperty('--x', x + 'px');
            sparkle.style.setProperty('--y', y + 'px');
            sparkle.style.setProperty('--rotation', Math.random() * 720 + 'deg');
            
            button.appendChild(sparkle);

            setTimeout(() => sparkle.remove(), 1200);
        }

        setTimeout(() => wave.remove(), 800);
    }

    function autoMorph() {
        // Morph first button (octagon-based buttons)
        const firstButtons = morphingButtons.filter((button, index) => index % 2 === 0);
        firstButtons.forEach(button => {
            if (button) {
                currentShapeIndex1 = (currentShapeIndex1 + 1) % shapes.length;
                const nextShape = shapes[currentShapeIndex1];
                morphToShape(button, nextShape.class, true);
            }
        });

        // Morph second button (emerald-based buttons) with slight delay
        setTimeout(() => {
            const secondButtons = morphingButtons.filter((button, index) => index % 2 === 1);
            secondButtons.forEach(button => {
                if (button) {
                    currentShapeIndex2 = (currentShapeIndex2 + 1) % shapes.length;
                    const nextShape = shapes[currentShapeIndex2];
                    morphToShape(button, nextShape.class, false);
                }
            });
        }, 1250); // 1.25 second delay between buttons
    }

    // Enhanced click effect for morphing buttons
    function handleButtonClick(button) {
        if (!button) return;

        // Intense flash
        const flash = document.createElement('div');
        flash.style.position = 'absolute';
        flash.style.top = '0';
        flash.style.left = '0';
        flash.style.right = '0';
        flash.style.bottom = '0';
        flash.style.background = 'rgba(255,255,255,0.9)';
        flash.style.clipPath = getComputedStyle(button).clipPath;
        flash.style.animation = 'intenseFlash 0.4s ease-out';
        flash.style.pointerEvents = 'none';
        flash.style.zIndex = '30';

        button.appendChild(flash);

        setTimeout(() => flash.remove(), 400);
    }

    // Add click handlers to all morphing buttons
    morphingButtons.forEach(button => {
        if (button) {
            button.addEventListener('click', () => handleButtonClick(button));
        }
    });

    // Start auto-morphing immediately
    // Initial morph to set different starting shapes
    autoMorph();

    // Then continue auto-morphing every 1 second
    setInterval(autoMorph, 1000);
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeGemstoneorphing);
} else {
    initializeGemstoneorphing();
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { initializeGemstoneorphing };
}
