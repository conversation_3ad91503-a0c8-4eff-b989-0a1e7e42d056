.hero-container {
    width: 100%;
    height: 89vh;
    position: relative;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.8);
}

.hero-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.hero-video,
.hero-image {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.16);
}



.hero-content {
    position: absolute;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: flex-start;
    z-index: 20;
    inset: 0px;
}

.hero-text-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
    margin-bottom: 1.25rem; /* 20px converted to rem */
    margin-left: 1.625rem; /* 26px converted to rem */
}

.hero-title {
    color: white;
    font-family: var(--font-dosis), sans-serif;
    font-size: 1.875rem; /* text-3xl */
    font-weight: 400; /* font-medium */
    text-transform: capitalize;
    line-height: 1.25; /* leading-tight */
    letter-spacing: 1px;
    margin-bottom: 0.5rem; /* mb-2 */
    filter: drop-shadow(2px 0.5px 3px rgba(0,0,0,1));
}

.hero-subtitle {
    color: white;
    font-family: var(--font-dosis), sans-serif;
    font-size: 1rem; /* text-base */
    font-weight: 400; /* font-medium */
    text-transform: capitalize;
    line-height: 1.25; /* leading-tight */
    letter-spacing: 1px;
    filter: drop-shadow(0px 2px 2px rgba(0,0,0,1));
}

.hero-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    margin-bottom: 2rem;
    margin-left: 1.375rem; /* 22px converted to rem (16px = 1rem) */
    margin-bottom: 1.5rem; /* 24px converted to rem */
}

.hero-button-left {
    z-index: 20;
}

.hero-button-right {
    z-index: 20;
}

/* Living Gem Button Animations */
@keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 var(--glow-size) var(--glow-color-1); }
    50% { box-shadow: 0 0 calc(var(--glow-size) * 1.5) var(--glow-color-2); }
}

@keyframes aurora-flow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes stars-twinkle {
    0% { opacity: 0.2; }
    50% { opacity: 1; }
    100% { opacity: 0.2; }
}

@keyframes core-flare {
    0% { transform: scale(0); opacity: 0.8; }
    80% { transform: scale(1.5); opacity: 0.2; }
    100% { transform: scale(2); opacity: 0; }
}

@keyframes button-vibrate {
    0% { transform: translate(0); }
    10% { transform: translate(-1px, -1px); }
    20% { transform: translate(1px, -1px); }
    30% { transform: translate(-1px, 1px); }
    40% { transform: translate(1px, 1px); }
    50% { transform: translate(-1px, -1px); }
    60% { transform: translate(1px, -1px); }
    70% { transform: translate(-1px, 1px); }
    80% { transform: translate(1px, 1px); }
    90% { transform: translate(-1px, -1px); }
    100% { transform: translate(0); }
}

.hero-button-wrapper {
    perspective: 250px;
    position: relative;
}

.hero-button {
    position: relative;
    width: 10rem; /* w-40 */
    height: 2.75rem; /* h-11 */
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-dosis), sans-serif;
    font-weight: 500;
    font-size: 0.875rem; /* text-sm */
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.5);
    cursor: pointer;

    border: 1px solid transparent;
    border-radius: 50px;

    background-image: var(--bg-gradient), linear-gradient(to bottom, #ffd700, #f0c000, #d9a400);
    background-origin: border-box;
    background-clip: content-box, border-box;

    --glow-size: 25px;
    --glow-color-1: rgba(255, 223, 0, 0.8);
    --glow-color-2: rgba(255, 215, 0, 1);

    /* animation: pulse-glow 4s infinite ease-in-out; */ /* Removed golden glow */
    transition: all 1.2s cubic-bezier(0.23, 1, 0.32, 1), box-shadow 0.4s ease, transform 0.3s ease-out;
    transform-style: preserve-3d;
    overflow: hidden;
    letter-spacing: 0.12em; /* increased tracking for premium feel */

    /* Default shape - will be overridden by shape classes */
    clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);
}

/* Mouse tracking highlight */
.hero-button::before {
    content: '';
    position: absolute;
    left: 0; top: 0;
    width: 100%; height: 100%;
    background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
                                rgba(255, 255, 255, 0.4),
                                transparent 40%);
    border-radius: 50px;
    opacity: 1;
    transition: opacity 0.4s;
    z-index: 2;
}

/* Core flare effect during unstable events */
.hero-button.is-unstable::before {
    animation: core-flare 0.4s ease-out forwards;
}

/* Aurora flow effect */
.hero-button::after {
    content: '';
    position: absolute;
    left: 0; top: 0;
    width: 100%; height: 100%;
    background: var(--aurora-gradient);
    background-size: 300% 300%;
    border-radius: 50px;
    animation: aurora-flow 12s infinite ease-in-out;
    opacity: 0.7;
    mix-blend-mode: screen;
    z-index: 1;
}

/* Crack overlay for unstable events */
.crack-overlay {
    position: absolute;
    top: 0; left: 0; width: 100%; height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg width="280" height="65" viewBox="0 0 280 65" xmlns="http://www.w3.org/2000/svg"><path d="M130 0 L140 25 L120 30 L150 65 M160 0 L155 30 L170 35 L160 65 M0 32 L280 33 M145 28 L165 37" stroke-width="1.5" stroke="white" fill="none" /></svg>');
    background-size: 100% 100%;
    z-index: 3;
    opacity: 0;
    transition: opacity 0.4s ease-out;
}

.hero-button.is-unstable .crack-overlay {
    opacity: 0.7;
    transition-duration: 0.1s;
}

/* Vibrate effect during unstable events */
.hero-button.is-unstable {
    animation: button-vibrate 0.4s ease-in-out;
}

.hero-button span {
    position: relative;
    z-index: 50;
    pointer-events: none;
    text-shadow:
        0 3px 12px rgba(0,0,0,1),
        0 0 15px rgba(64, 224, 255, 0.4);
    transition: all 0.6s ease;
}

/* Stars effect */
.stars {
    position: absolute;
    top: 0; left: 0;
    width: 1px; height: 1px;
    background: transparent;
    box-shadow: var(--star-shadows);
    animation: stars-twinkle 6s infinite ease-in-out;
    z-index: 0;
}

.stars:nth-child(2) {
    animation-delay: -2s;
    box-shadow: var(--star-shadows-2);
}

.hero-button:active {
    transform: scale(0.96) !important;
    transition-duration: 0.1s;
}

.hero-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
}

/* Button style variants */
.dark-style {
    --bg-gradient: linear-gradient(to bottom, #434343, #000000);
    --aurora-gradient: linear-gradient(45deg, #888, #555, #fff, #555, #888);
}

.sapphire-style {
    --bg-gradient: linear-gradient(180deg, #4a8cfd, #0d3a80);
    --aurora-gradient: linear-gradient(45deg, #00d2ff, #3a7bd5, #ffffff, #3a7bd5, #00d2ff);
}

.hero-button-secondary {
    position: relative;
    width: 10rem; /* w-40 */
    height: 2.75rem; /* h-11 */
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-dosis), sans-serif;
    font-weight: 500;
    font-size: 0.875rem; /* text-sm */
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.5);
    cursor: pointer;

    border: 1px solid transparent;
    border-radius: 50px;

    background-image: var(--bg-gradient), linear-gradient(to bottom, #ffd700, #f0c000, #d9a400);
    background-origin: border-box;
    background-clip: content-box, border-box;

    --glow-size: 25px;
    --glow-color-1: rgba(255, 223, 0, 0.8);
    --glow-color-2: rgba(255, 215, 0, 1);

    /* animation: pulse-glow 4s infinite ease-in-out; */ /* Removed golden glow */
    transition: all 1.2s cubic-bezier(0.23, 1, 0.32, 1), box-shadow 0.4s ease, transform 0.3s ease-out;
    transform-style: preserve-3d;
    overflow: hidden;
    letter-spacing: 0.12em; /* increased tracking for premium feel */

    /* Default shape - will be overridden by shape classes */
    clip-path: polygon(12% 0%, 88% 0%, 100% 12%, 100% 88%, 88% 100%, 12% 100%, 0% 88%, 0% 12%);
}

/* Mouse tracking highlight for secondary button */
.hero-button-secondary::before {
    content: '';
    position: absolute;
    left: 0; top: 0;
    width: 100%; height: 100%;
    background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
                                rgba(255, 255, 255, 0.4),
                                transparent 40%);
    border-radius: 50px;
    opacity: 1;
    transition: opacity 0.4s;
    z-index: 2;
}

/* Core flare effect during unstable events for secondary button */
.hero-button-secondary.is-unstable::before {
    animation: core-flare 0.4s ease-out forwards;
}

/* Crack overlay for secondary button unstable events */
.hero-button-secondary.is-unstable .crack-overlay {
    opacity: 0.7;
    transition-duration: 0.1s;
}

/* Vibrate effect during unstable events for secondary button */
.hero-button-secondary.is-unstable {
    animation: button-vibrate 0.4s ease-in-out;
}

/* Aurora flow effect for secondary button */
.hero-button-secondary::after {
    content: '';
    position: absolute;
    left: 0; top: 0;
    width: 100%; height: 100%;
    background: var(--aurora-gradient);
    background-size: 300% 300%;
    border-radius: 50px;
    animation: aurora-flow 12s infinite ease-in-out;
    opacity: 0.7;
    mix-blend-mode: screen;
    z-index: 1;
}

.hero-button-secondary span {
    position: relative;
    z-index: 50;
    pointer-events: none;
    text-shadow:
        0 3px 12px rgba(0,0,0,1),
        0 0 15px rgba(64, 224, 255, 0.4);
    transition: all 0.6s ease;
}

.hero-button-secondary:active {
    transform: scale(0.96) !important;
    transition-duration: 0.1s;
}

.hero-button-secondary:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
}

/* Gemstone Shape Classes - Applied to Button Elements */
.hero-button.shape-octagon,
.hero-button-secondary.shape-octagon {
    clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);
}

.hero-button.shape-emerald,
.hero-button-secondary.shape-emerald {
    clip-path: polygon(12% 0%, 88% 0%, 100% 12%, 100% 88%, 88% 100%, 12% 100%, 0% 88%, 0% 12%);
}

.hero-button.shape-rectangle,
.hero-button-secondary.shape-rectangle {
    clip-path: polygon(8% 0%, 92% 0%, 100% 8%, 100% 92%, 92% 100%, 8% 100%, 0% 92%, 0% 8%);
}

.hero-button.shape-triangle,
.hero-button-secondary.shape-triangle {
    clip-path: polygon(50% 0%, 95% 70%, 75% 100%, 25% 100%, 5% 70%);
}

.hero-button.shape-diamond,
.hero-button-secondary.shape-diamond {
    clip-path: polygon(50% 0%, 80% 15%, 100% 50%, 80% 85%, 50% 100%, 20% 85%, 0% 50%, 20% 15%);
}

.hero-button.shape-heart,
.hero-button-secondary.shape-heart {
    clip-path: polygon(50% 25%, 25% 0%, 0% 25%, 0% 62.5%, 50% 100%, 100% 62.5%, 100% 25%, 75% 0%);
}

/* Crystal Layer Effects */
.crystal-layer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    clip-path: inherit;
    pointer-events: none;
    z-index: 1;
}

.crystal-facets {
    background:
        linear-gradient(45deg,
            rgba(255,255,255,0.9) 0%,
            rgba(255,255,255,0.4) 15%,
            transparent 25%,
            transparent 75%,
            rgba(255,255,255,0.3) 85%,
            rgba(255,255,255,0.8) 100%);
}

.crystal-highlight {
    background:
        radial-gradient(ellipse at 30% 20%,
            rgba(255,255,255,0.95) 0%,
            rgba(255,255,255,0.7) 20%,
            rgba(255,255,255,0.3) 50%,
            transparent 100%);
    filter: blur(8px);
    opacity: 0.9;
}

.light-refraction {
    background:
        linear-gradient(60deg,
            transparent 0%,
            rgba(255,0,150,0.15) 20%,
            rgba(0,255,255,0.15) 40%,
            rgba(255,255,0,0.15) 60%,
            rgba(150,0,255,0.15) 80%,
            transparent 100%);
    opacity: 0;
    transition: all 0.8s ease;
    animation: refract 3s infinite;
}

@keyframes refract {
    0%, 100% { transform: translateX(-150%) rotate(0deg); opacity: 0; }
    30% { opacity: 0.8; }
    70% { opacity: 0.8; }
    100% { transform: translateX(150%) rotate(5deg); opacity: 0; }
}

/* Dynamic Sparkle System */
.sparkle-system {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.sparkle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, #ffffff 0%, #40e0ff 70%, transparent 100%);
    border-radius: 50%;
    opacity: 0;
    animation: dynamicSparkle 4s infinite;
    box-shadow: 0 0 10px rgba(255,255,255,0.8);
}

@keyframes dynamicSparkle {
    0%, 100% {
        opacity: 0;
        transform: scale(0) rotate(0deg);
    }
    20% {
        opacity: 1;
        transform: scale(1.5) rotate(90deg);
        box-shadow: 0 0 20px rgba(255,255,255,1);
    }
    40% {
        opacity: 0.7;
        transform: scale(1) rotate(180deg);
    }
    60% {
        opacity: 1;
        transform: scale(1.8) rotate(270deg);
        box-shadow: 0 0 25px rgba(64, 224, 255, 0.9);
    }
    80% {
        opacity: 0.5;
        transform: scale(0.8) rotate(360deg);
    }
}

/* Multiple sparkles positioned dynamically */
.sparkle:nth-child(1) { top: 25%; left: 30%; animation-delay: 0s; }
.sparkle:nth-child(2) { top: 60%; right: 25%; animation-delay: 1.3s; }
.sparkle:nth-child(3) { top: 40%; left: 60%; animation-delay: 2.1s; }
.sparkle:nth-child(4) { bottom: 30%; left: 25%; animation-delay: 3.2s; }
.sparkle:nth-child(5) { top: 70%; right: 40%; animation-delay: 0.7s; }
.sparkle:nth-child(6) { top: 35%; right: 60%; animation-delay: 1.8s; }

/* Particle and effect styles */
.particle, .shockwave, .venting-shard {
    position: fixed;
    border-radius: 50%;
    pointer-events: none;
}

.shockwave {
    z-index: 9999;
    transition: transform 0.3s ease-out, opacity 0.3s ease-out;
}

.particle {
    z-index: 9999;
    transition: transform 1s cubic-bezier(0.175, 0.885, 0.32, 1.275), opacity 1s ease-out;
}

.venting-shard {
    z-index: 10000;
    background: white;
    clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
    transition: transform 0.5s ease-out, opacity 0.5s ease-out;
}

/* Morphing Animation Styles */
@keyframes morphWave {
    0% {
        opacity: 0;
        transform: scale(0.5) rotate(0deg);
    }
    50% {
        opacity: 1;
        transform: scale(1.2) rotate(180deg);
    }
    100% {
        opacity: 0;
        transform: scale(1.5) rotate(360deg);
    }
}

@keyframes morphSparkle {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 1;
    }
    60% {
        opacity: 1;
        transform: translate(calc(-50% + var(--x) * 0.8), calc(-50% + var(--y) * 0.8)) scale(1.5) rotate(calc(var(--rotation) * 0.6));
    }
    100% {
        transform: translate(calc(-50% + var(--x)), calc(-50% + var(--y))) scale(0) rotate(var(--rotation));
        opacity: 0;
    }
}

@keyframes intenseFlash {
    0% {
        opacity: 0;
        transform: scale(0.9);
    }
    30% {
        opacity: 1;
        transform: scale(1.1);
    }
    100% {
        opacity: 0;
        transform: scale(1.3);
    }
}



/* Media Queries */
/* Removed 640px breakpoint */

/* Tablet Breakpoint (768px) */
@media (min-width: 768px) {
    .hero-text-content {
        margin-left: 2rem; /* Slightly increased from mobile, merged from previous 640px styles */
        margin-bottom: 1.75rem; /* Increased from default, incorporating 640px margin-bottom */
    }

    .hero-title {
        font-size: 2.25rem; /* Kept from 640px breakpoint */
        margin-bottom: 0.75rem; /* Kept from 640px breakpoint */
    }

    .hero-subtitle {
        font-size: 1.25rem; /* Kept from 640px breakpoint */
    }

    .hero-buttons-container {
        margin-left: 2rem; /* Slightly increased from mobile */
        margin-bottom: 2rem; /* Slightly increased */
        gap: 1.5rem; /* Increased gap between buttons, merged from 640px */
        flex-wrap: wrap;
    }

    .hero-button,
    .hero-button-secondary {
        width: 11rem; /* Slightly wider than mobile */
        height: 2.9rem; /* Slightly taller than mobile */
        font-size: 0.95rem; /* Slightly larger font */
    }
} 

/* Laptop Breakpoint (1024px) */
@media (min-width: 1024px) {
    .hero-text-content {
        margin-left: 2.5rem; /* Further increased */
        margin-bottom: 2rem; /* Further increased */
    }

    .hero-buttons-container {
        margin-left: 2.5rem; /* Further increased */
        margin-bottom: 2.25rem; /* Further increased */
        gap: 1.5rem; /* Increased gap between buttons */
    }

    .hero-button,
    .hero-button-secondary {
        width: 12rem; /* Wider buttons */
        height: 3.1rem; /* Taller buttons */
        font-size: 1rem; /* Larger font */
    }
} 